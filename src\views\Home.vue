<template>
  <div class="home">
    <div class="welcome-section">
      <h1>欢迎来到购物商城管理系统</h1>
      <p v-if="isLoggedIn">您好，{{ username }}！欢迎使用管理系统。</p>
      <p v-else>请先登录以使用管理功能。</p>
    </div>

    <div class="feature-cards" v-if="isLoggedIn">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card class="feature-card" @click="goToUsers">
            <div class="card-content">
              <el-icon class="card-icon"><User /></el-icon>
              <h3>用户管理</h3>
              <p>管理系统用户，包括添加、编辑、删除用户信息</p>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="8">
          <el-card class="feature-card" @click="goToCategories">
            <div class="card-content">
              <el-icon class="card-icon"><Menu /></el-icon>
              <h3>类别管理</h3>
              <p>管理商品类别，支持多级分类和层级管理</p>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="8">
          <el-card class="feature-card" @click="goToGoods">
            <div class="card-content">
              <el-icon class="card-icon"><ShoppingBag /></el-icon>
              <h3>商品展示</h3>
              <p>查看商品列表，浏览推荐商品信息</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div class="login-prompt" v-else>
      <el-card>
        <div class="prompt-content">
          <el-icon class="prompt-icon"><Lock /></el-icon>
          <h3>需要登录</h3>
          <p>请先登录以访问管理功能</p>
          <el-button type="primary" @click="goToLogin">
            立即登录
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 商品展示区域 -->
    <div class="goods-section">
      <GoodsView />
    </div>
  </div>
</template>

<script setup name="HomeView">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { User, Menu, ShoppingBag, Lock } from '@element-plus/icons-vue'
import GoodsView from './goods.vue'

const router = useRouter()

// 计算属性
const isLoggedIn = computed(() => {
  return localStorage.getItem('isLoggedIn') === 'true'
})

const username = computed(() => {
  return localStorage.getItem('username') || '用户'
})

// 导航方法
const goToUsers = () => {
  router.push('/users')
}

const goToCategories = () => {
  router.push('/categories')
}

const goToGoods = () => {
  router.push('/goods')
}

const goToLogin = () => {
  router.push('/login')
}
</script>

<style scoped>
.home {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-section {
  text-align: center;
  margin-bottom: 40px;
  padding: 40px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 10px;
}

.welcome-section h1 {
  font-size: 2.5em;
  margin-bottom: 20px;
  font-weight: bold;
}

.welcome-section p {
  font-size: 1.2em;
  opacity: 0.9;
}

.feature-cards {
  margin-bottom: 40px;
}

.feature-card {
  cursor: pointer;
  transition: all 0.3s ease;
  height: 200px;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.card-content {
  text-align: center;
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.card-icon {
  font-size: 3em;
  color: #409eff;
  margin-bottom: 15px;
}

.card-content h3 {
  font-size: 1.3em;
  margin-bottom: 10px;
  color: #333;
}

.card-content p {
  color: #666;
  font-size: 0.9em;
  line-height: 1.5;
}

.login-prompt {
  margin-bottom: 40px;
}

.prompt-content {
  text-align: center;
  padding: 40px;
}

.prompt-icon {
  font-size: 4em;
  color: #f56c6c;
  margin-bottom: 20px;
}

.prompt-content h3 {
  font-size: 1.5em;
  margin-bottom: 15px;
  color: #333;
}

.prompt-content p {
  color: #666;
  margin-bottom: 25px;
}

.goods-section {
  margin-top: 40px;
  padding-top: 40px;
  border-top: 1px solid #eee;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-section h1 {
    font-size: 2em;
  }
  
  .welcome-section p {
    font-size: 1em;
  }
  
  .feature-cards .el-col {
    margin-bottom: 20px;
  }
}
</style>
