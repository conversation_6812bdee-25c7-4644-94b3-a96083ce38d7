import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/Home.vue'
import Login from '../views/Login.vue'
import AdminLayout from '../components/AdminLayout.vue'
import AdminDashboard from '../views/AdminDashboard.vue'
import UserManagement from '../views/UserManagement.vue'
import CategoryManagement from '../views/category.vue'
import GoodsView from '../views/goods.vue'

const routes = [
  {
    path: '/',
    redirect: '/admin'
  },
  {
    path: '/home',
    name: 'Home',
    component: Home
  },
  {
    path: '/login',
    name: 'Login',
    component: Login
  },
  {
    path: '/admin',
    component: AdminLayout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'AdminDashboard',
        component: AdminDashboard
      },
      {
        path: 'users',
        name: 'UserManagement',
        component: UserManagement
      },
      {
        path: 'categories',
        name: 'CategoryManagement',
        component: CategoryManagement
      },
      {
        path: 'goods',
        name: 'Goods',
        component: GoodsView
      }
    ]
  },
  // 兼容旧路由
  {
    path: '/users',
    redirect: '/admin/users'
  },
  {
    path: '/categories',
    redirect: '/admin/categories'
  },
  {
    path: '/goods',
    redirect: '/admin/goods'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫 - 检查登录状态
router.beforeEach((to, from, next) => {
  const isLoggedIn = localStorage.getItem('isLoggedIn')

  // 如果路由需要认证且用户未登录，跳转到登录页
  if (to.meta.requiresAuth && !isLoggedIn) {
    next('/login')
  } else {
    next()
  }
})

export default router