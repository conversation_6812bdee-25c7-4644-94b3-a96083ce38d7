{"name": "shopping_front", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^1.11.0", "core-js": "^3.8.3", "element-plus": "^2.11.1", "vue": "^3.2.13", "vue-router": "^4.5.1"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {"vue/multi-word-component-names": "off", "no-unused-vars": "warn"}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}