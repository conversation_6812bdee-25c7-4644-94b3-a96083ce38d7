// utils/authApi.js
import request from './request.js'

// 认证相关API
export const authApi = {
  // 用户登录
  login(loginData) {
    return request.post('/auth/login', loginData)
  },

  // 用户注册
  register(registerData) {
    return request.post('/auth/register', registerData)
  },

  // 获取当前用户信息
  getCurrentUser() {
    return request.get('/auth/current')
  },

  // 刷新token
  refreshToken() {
    return request.post('/auth/refresh')
  },

  // 退出登录
  logout() {
    return request.post('/auth/logout')
  },

  // 修改密码
  changePassword(passwordData) {
    return request.post('/auth/changePassword', passwordData)
  }
}

export default authApi
