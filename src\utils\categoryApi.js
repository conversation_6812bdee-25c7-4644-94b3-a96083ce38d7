// utils/categoryApi.js
import request from './request.js'

// 类别管理相关API
export const categoryApi = {
  // 获取类别列表
  getCategoryList(params = {}) {
    return request.get('/category/list', params)
  },

  // 获取类别树形结构
  getCategoryTree() {
    return request.get('/category/tree')
  },

  // 根据ID获取类别详情
  getCategoryById(id) {
    return request.get(`/category/${id}`)
  },

  // 添加类别
  addCategory(categoryData) {
    return request.post('/category/add', categoryData)
  },

  // 更新类别信息
  updateCategory(id, categoryData) {
    return request.put(`/category/update/${id}`, categoryData)
  },

  // 删除类别
  deleteCategory(id) {
    return request.delete(`/category/delete/${id}`)
  },

  // 批量删除类别
  batchDeleteCategories(ids) {
    return request.post('/category/batchDelete', { ids })
  },

  // 搜索类别
  searchCategories(keyword, params = {}) {
    return request.get('/category/search', { keyword, ...params })
  },

  // 更新类别状态（启用/禁用）
  updateCategoryStatus(id, status) {
    return request.put(`/category/status/${id}`, { status })
  },

  // 获取父级类别列表
  getParentCategories() {
    return request.get('/category/parents')
  }
}

export default categoryApi
