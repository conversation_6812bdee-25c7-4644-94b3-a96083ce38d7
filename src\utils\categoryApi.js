// utils/categoryApi.js
import request from './request.js'

// 类别管理相关API
export const categoryApi = {
  // 获取类别列表
  getCategoryList() {
    return request.get('/categoryAPI/findAll')
  },

  // 获取类别树形结构
  getCategoryTree() {
    return request.get('/categoryAPI/tree')
  },

  // 根据ID获取类别详情
  getCategoryById(id) {
    return request.get(`/categoryAPI/${id}`)
  },

  // 添加类别 - 使用统一的保存或更新接口
  addCategory(categoryData) {
    // 新增时不传id
    // eslint-disable-next-line no-unused-vars
    const { id, ...categoryDataWithoutId } = categoryData
    return request.post('/categoryAPI/saveOrUpdate', categoryDataWithoutId)
  },

  // 更新类别信息 - 使用统一的保存或更新接口
  updateCategory(id, categoryData) {
    // 更新时必须包含ID
    return request.post('/categoryAPI/saveOrUpdate', { ...categoryData, id })
  },

  // 删除类别
  deleteCategory(id) {
    return request.delete(`/categoryAPI/delete/${id}`)
  },

  // 批量删除类别
  batchDeleteCategories(ids) {
    return request.post('/categoryAPI/batchDelete', { ids })
  },

  // 搜索类别（前端过滤）
  searchCategories(keyword, params = {}) {
    return request.get('/categoryAPI/search', { keyword, ...params })
  },

  // 更新类别状态（如果后端支持）
  updateCategoryStatus(id, status) {
    return request.put(`/categoryAPI/status/${id}`, { status })
  },

  // 获取父级类别列表
  getParentCategories() {
    return request.get('/categoryAPI/parents')
  }
}

export default categoryApi
