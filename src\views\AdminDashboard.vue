<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>管理后台首页</h1>
      <p>欢迎使用在线购物管理系统</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon user-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ userCount }}</div>
              <div class="stats-label">用户总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon category-icon">
              <el-icon><Menu /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ categoryCount }}</div>
              <div class="stats-label">分类总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon goods-icon">
              <el-icon><Goods /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ goodsCount }}</div>
              <div class="stats-label">商品总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon order-icon">
              <el-icon><ShoppingCart /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ orderCount }}</div>
              <div class="stats-label">订单总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快捷操作 -->
    <el-card class="quick-actions" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>快捷操作</span>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <el-button 
            type="primary" 
            size="large" 
            class="quick-btn"
            @click="$router.push('/admin/users')"
          >
            <el-icon><User /></el-icon>
            用户管理
          </el-button>
        </el-col>
        
        <el-col :span="6">
          <el-button 
            type="success" 
            size="large" 
            class="quick-btn"
            @click="$router.push('/admin/categories')"
          >
            <el-icon><Menu /></el-icon>
            分类管理
          </el-button>
        </el-col>
        
        <el-col :span="6">
          <el-button 
            type="warning" 
            size="large" 
            class="quick-btn"
            @click="$router.push('/admin/goods')"
          >
            <el-icon><Goods /></el-icon>
            商品管理
          </el-button>
        </el-col>
        
        <el-col :span="6">
          <el-button 
            type="info" 
            size="large" 
            class="quick-btn"
            @click="$router.push('/admin/orders')"
          >
            <el-icon><ShoppingCart /></el-icon>
            订单管理
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 系统信息 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>系统信息</span>
            </div>
          </template>
          
          <div class="system-info">
            <div class="info-item">
              <span class="info-label">系统版本：</span>
              <span class="info-value">v1.0.0</span>
            </div>
            <div class="info-item">
              <span class="info-label">运行环境：</span>
              <span class="info-value">开发环境</span>
            </div>
            <div class="info-item">
              <span class="info-label">数据库：</span>
              <span class="info-value">MySQL 8.0</span>
            </div>
            <div class="info-item">
              <span class="info-label">服务器：</span>
              <span class="info-value">Spring Boot 2.7</span>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近操作</span>
            </div>
          </template>
          
          <div class="recent-actions">
            <div class="action-item">
              <el-icon class="action-icon"><User /></el-icon>
              <span>添加了新用户</span>
              <span class="action-time">2分钟前</span>
            </div>
            <div class="action-item">
              <el-icon class="action-icon"><Menu /></el-icon>
              <span>更新了商品分类</span>
              <span class="action-time">5分钟前</span>
            </div>
            <div class="action-item">
              <el-icon class="action-icon"><Goods /></el-icon>
              <span>添加了新商品</span>
              <span class="action-time">10分钟前</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { User, Menu, Goods, ShoppingCart } from '@element-plus/icons-vue'
import { userApi } from '@/utils/userApi'
import { categoryApi } from '@/utils/categoryApi'

// 响应式数据
const userCount = ref(0)
const categoryCount = ref(0)
const goodsCount = ref(0)
const orderCount = ref(0)

// 获取统计数据
const getStatistics = async () => {
  try {
    // 获取用户数量
    const users = await userApi.getUserList()
    userCount.value = Array.isArray(users) ? users.length : 0
    
    // 获取分类数量
    const categories = await categoryApi.getCategoryList()
    categoryCount.value = Array.isArray(categories) ? categories.length : 0
    
    // 商品和订单数量暂时设为0，等后续接口完善
    goodsCount.value = 0
    orderCount.value = 0
  } catch (error) {
    console.error('获取统计数据失败：', error)
  }
}

// 组件挂载时获取数据
onMounted(() => {
  getStatistics()
})
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.dashboard-header {
  margin-bottom: 30px;
}

.dashboard-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.dashboard-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  border-radius: 8px;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.user-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.category-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.goods-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.order-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.quick-actions .quick-btn {
  width: 100%;
  height: 60px;
  font-size: 16px;
  border-radius: 8px;
}

.card-header {
  font-weight: bold;
  color: #303133;
}

.system-info .info-item {
  display: flex;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.system-info .info-item:last-child {
  border-bottom: none;
}

.info-label {
  color: #909399;
  font-size: 14px;
}

.info-value {
  color: #303133;
  font-size: 14px;
  font-weight: 500;
}

.recent-actions .action-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.recent-actions .action-item:last-child {
  border-bottom: none;
}

.action-icon {
  margin-right: 12px;
  color: #409EFF;
}

.action-time {
  margin-left: auto;
  color: #909399;
  font-size: 12px;
}
</style>
