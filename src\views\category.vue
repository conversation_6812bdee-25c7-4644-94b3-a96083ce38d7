<template>
  <div class="category-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="page-title">
        <h2>类别管理</h2>
        <p>管理商品分类信息，支持多级分类结构</p>
      </div>
    </div>

    <!-- 操作区域 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>类别列表</span>
          <div class="header-actions">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索类别名称"
              style="width: 300px; margin-right: 10px"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-button type="primary" @click="showAddDialog">
              <el-icon><Plus /></el-icon>
              添加类别
            </el-button>
          </div>
        </div>
      </template>

    <!-- 类别列表表格 -->
    <el-table
      :data="categoryList"
      v-loading="loading"
      style="width: 100%"
      row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="name" label="类别名称" width="200" />
      <el-table-column prop="description" label="描述" min-width="200" />
      <el-table-column prop="sort" label="排序" width="80" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            @change="handleStatusChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="180" />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="showEditDialog(scope.row)">
            编辑
          </el-button>
          <el-button
            size="small"
            type="success"
            @click="showAddChildDialog(scope.row)"
          >
            添加子类别
          </el-button>
          <el-button
            size="small"
            type="danger"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 批量操作 -->
    <div class="batch-actions" v-if="selectedCategories.length > 0">
      <el-button type="danger" @click="handleBatchDelete">
        批量删除 ({{ selectedCategories.length }})
      </el-button>
    </div>

      <!-- 类别总数显示 -->
      <div class="total-info" style="margin-top: 20px; text-align: right">
        <el-tag type="info">共 {{ total }} 个类别</el-tag>
      </div>
    </el-card>

    <!-- 添加/编辑类别对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
      @close="resetForm"
    >
      <el-form
        ref="categoryFormRef"
        :model="categoryForm"
        :rules="categoryRules"
        label-width="100px"
      >
        <el-form-item label="类别名称" prop="name">
          <el-input v-model="categoryForm.name" placeholder="请输入类别名称" />
        </el-form-item>
        <el-form-item label="父级类别" prop="parentId">
          <el-select
            v-model="categoryForm.parentId"
            placeholder="请选择父级类别（可选）"
            clearable
            style="width: 100%"
          >
            <el-option label="无（顶级类别）" :value="0" />
            <el-option
              v-for="category in parentCategories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="categoryForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入类别描述"
          />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number
            v-model="categoryForm.sort"
            :min="0"
            :max="999"
            placeholder="排序值"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="categoryForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="CategoryManagement">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus } from '@element-plus/icons-vue'
import categoryApi from '@/utils/categoryApi'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const categoryList = ref([])
const parentCategories = ref([])
const selectedCategories = ref([])
const searchKeyword = ref('')
const total = ref(0)

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('添加类别')
const isEdit = ref(false)
const categoryFormRef = ref()

// 类别表单数据
const categoryForm = reactive({
  id: null,
  name: '',
  parentId: 0,
  description: '',
  sort: 0,
  status: 1
})

// 表单验证规则
const categoryRules = {
  name: [
    { required: true, message: '请输入类别名称', trigger: 'blur' },
    { min: 2, max: 50, message: '类别名称长度为2-50个字符', trigger: 'blur' }
  ],
  sort: [
    { required: true, message: '请输入排序值', trigger: 'blur' },
    { type: 'number', message: '排序值必须为数字', trigger: 'blur' }
  ]
}

// 获取类别列表
const getCategoryList = async () => {
  loading.value = true
  try {
    const response = await categoryApi.getCategoryList()
    // 根据后端返回的数据结构，response直接是类别数组
    categoryList.value = Array.isArray(response) ? response : []

    // 如果有搜索关键词，进行前端过滤
    if (searchKeyword.value) {
      categoryList.value = categoryList.value.filter(category =>
        category.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
        (category.description && category.description.toLowerCase().includes(searchKeyword.value.toLowerCase()))
      )
    }

    total.value = categoryList.value.length
  } catch (error) {
    ElMessage.error('获取类别列表失败：' + error.message)
    categoryList.value = []
  } finally {
    loading.value = false
  }
}

// 获取父级类别列表
const getParentCategories = async () => {
  try {
    const response = await categoryApi.getParentCategories()
    parentCategories.value = response || []
  } catch (error) {
    console.error('获取父级类别失败：', error.message)
    parentCategories.value = []
  }
}

// 搜索类别
const handleSearch = () => {
  getCategoryList()
}

// 显示添加对话框
const showAddDialog = () => {
  dialogTitle.value = '添加类别'
  isEdit.value = false
  dialogVisible.value = true
  resetForm()
}

// 显示添加子类别对话框
const showAddChildDialog = (parentRow) => {
  dialogTitle.value = '添加子类别'
  isEdit.value = false
  dialogVisible.value = true
  resetForm()
  categoryForm.parentId = parentRow.id
}

// 显示编辑对话框
const showEditDialog = (row) => {
  dialogTitle.value = '编辑类别'
  isEdit.value = true
  dialogVisible.value = true
  Object.assign(categoryForm, row)
}

// 重置表单
const resetForm = () => {
  if (categoryFormRef.value) {
    categoryFormRef.value.resetFields()
  }
  Object.assign(categoryForm, {
    id: null,
    name: '',
    parentId: 0,
    description: '',
    sort: 0,
    status: 1
  })
}

// 提交表单
const handleSubmit = async () => {
  if (!categoryFormRef.value) return

  // 先进行前端基础验证
  try {
    await categoryFormRef.value.validate()
  } catch (error) {
    ElMessage.error('请填写正确的表单信息')
    return
  }

  // 前端验证通过后，调用后端接口进行整体验证和提交
  submitLoading.value = true
  try {
    if (isEdit.value) {
      // 更新类别：传递完整的类别数据包括id
      await categoryApi.updateCategory(categoryForm.id, categoryForm)
      ElMessage.success('类别更新成功')
    } else {
      // 添加类别：不传id
      await categoryApi.addCategory(categoryForm)
      ElMessage.success('类别添加成功')
    }

    // 操作成功后关闭对话框并刷新列表
    dialogVisible.value = false
    getCategoryList()
    getParentCategories() // 刷新父级类别列表
  } catch (error) {
    // 显示后端返回的具体错误信息
    ElMessage.error('操作失败：' + (error.message || '未知错误'))
  } finally {
    submitLoading.value = false
  }
}

// 删除类别
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除类别 "${row.name}" 吗？删除后其子类别也将被删除！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await categoryApi.deleteCategory(row.id)
    ElMessage.success('删除成功')
    getCategoryList()
    getParentCategories() // 刷新父级类别列表
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败：' + error.message)
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedCategories.value.length} 个类别吗？`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const ids = selectedCategories.value.map(category => category.id)
    await categoryApi.batchDeleteCategories(ids)
    ElMessage.success('批量删除成功')
    selectedCategories.value = []
    getCategoryList()
    getParentCategories() // 刷新父级类别列表
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败：' + error.message)
    }
  }
}

// 状态变更
const handleStatusChange = async (row) => {
  try {
    await categoryApi.updateCategoryStatus(row.id, row.status)
    ElMessage.success('状态更新成功')
  } catch (error) {
    ElMessage.error('状态更新失败：' + error.message)
    // 恢复原状态
    row.status = row.status === 1 ? 0 : 1
  }
}

// 选择变更
const handleSelectionChange = (selection) => {
  selectedCategories.value = selection
}

// 组件挂载时获取数据
onMounted(() => {
  getCategoryList()
  getParentCategories()
})
</script>

<style scoped>
.category-management {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.table-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-weight: bold;
  color: #303133;
}

.header-actions {
  display: flex;
  align-items: center;
}

.batch-actions {
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.total-info {
  padding: 10px 0;
  border-top: 1px solid #ebeef5;
}
</style>