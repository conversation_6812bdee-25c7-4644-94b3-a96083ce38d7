<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <h2>🛒 购物商城</h2>
        <p>欢迎登录</p>
      </div>
      
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @keyup.enter="handleLogin"
      >
        <!-- 用户名输入框 -->
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            prefix-icon="User"
            clearable
            size="large"
          />
        </el-form-item>
        
        <!-- 密码输入框 -->
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            prefix-icon="Lock"
            show-password
            size="large"
          />
        </el-form-item>
        
        <!-- 验证码 -->
        <el-form-item prop="captcha" class="captcha-item">
          <div class="captcha-wrapper">
            <el-input
              v-model="loginForm.captcha"
              placeholder="请输入验证码"
              prefix-icon="Key"
              size="large"
              class="captcha-input"
            />
            <div class="captcha-image" @click="refreshCaptcha">
              {{ captchaText }}
            </div>
          </div>
        </el-form-item>
        
        <!-- 记住密码和忘记密码 -->
        <el-form-item>
          <div class="login-options">
            <el-checkbox v-model="rememberMe">记住密码</el-checkbox>
            <el-link type="primary" @click="handleForgetPassword">忘记密码？</el-link>
          </div>
        </el-form-item>
        
        <!-- 登录按钮 -->
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            class="login-button"
            :loading="loading"
            @click="handleLogin"
            block
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
        
        <!-- 其他登录方式 -->
        <el-form-item>
          <div class="other-login">
            <span>其他登录方式：</span>
            <el-link type="primary" @click="handleWechatLogin">微信登录</el-link>
            <el-divider direction="vertical"></el-divider>
            <el-link type="primary" @click="handleQQLogin">QQ登录</el-link>
          </div>
        </el-form-item>
        
        <!-- 注册链接 -->
        <el-form-item>
          <div class="register-link">
            还没有账号？
            <el-link type="primary" @click="handleRegister">立即注册</el-link>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElNotification } from 'element-plus'

// 表单引用
const loginFormRef = ref()

// 路由实例
const router = useRouter()

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: '',
  captcha: ''
})

// 记住密码
const rememberMe = ref(false)

// 加载状态
const loading = ref(false)

// 验证码文本
const captchaText = ref('')

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度为6-20个字符', trigger: 'blur' }
  ],
  captcha: [
    { required: true, message: '请输入验证码', trigger: 'blur' }
  ]
}

// 生成随机验证码
const generateCaptcha = () => {
  const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789'
  let result = ''
  for (let i = 0; i < 4; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  captchaText.value = result
  loginForm.captcha = ''
}

// 刷新验证码
const refreshCaptcha = () => {
  generateCaptcha()
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  await loginFormRef.value.validate((valid) => {
    if (valid) {
      // 验证验证码
      if (loginForm.captcha.toLowerCase() !== captchaText.value.toLowerCase()) {
        ElMessage.error('验证码错误')
        refreshCaptcha()
        return
      }
      
      // 模拟登录过程
      loading.value = true
      
      setTimeout(() => {
        loading.value = false
        
        // 模拟登录成功
        if (loginForm.username === 'admin' && loginForm.password === '123456') {
          ElNotification({
            title: '登录成功',
            message: `欢迎回来，${loginForm.username}！`,
            type: 'success',
            duration: 2000
          })
          
          // 保存登录状态（实际项目中应该保存token）
          localStorage.setItem('isLoggedIn', 'true')
          localStorage.setItem('username', loginForm.username)
          
          // 跳转到首页
          router.push('/')
        } else {
          ElMessage.error('用户名或密码错误')
          refreshCaptcha()
        }
      }, 1500)
    } else {
      ElMessage.error('请填写正确的登录信息')
      return false
    }
  })
}

// 忘记密码
const handleForgetPassword = () => {
  ElMessage.info('忘记密码功能开发中...')
}

// 微信登录
const handleWechatLogin = () => {
  ElMessage.info('微信登录功能开发中...')
}

// QQ登录
const handleQQLogin = () => {
  ElMessage.info('QQ登录功能开发中...')
}

// 注册
const handleRegister = () => {
  ElMessage.info('注册功能开发中...')
  // router.push('/register')
}

// 组件挂载时生成验证码
onMounted(() => {
  generateCaptcha()
  
  // 检查是否记住密码
  const savedUsername = localStorage.getItem('savedUsername')
  if (savedUsername) {
    loginForm.username = savedUsername
    rememberMe.value = true
  }
})
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-box {
  width: 100%;
  max-width: 400px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 10px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  padding: 40px 30px;
  backdrop-filter: blur(10px);
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h2 {
  color: #333;
  margin-bottom: 10px;
  font-size: 24px;
}

.login-header p {
  color: #666;
  font-size: 14px;
}

.login-form {
  width: 100%;
}

.captcha-item :deep(.el-form-item__content) {
  display: block;
}

.captcha-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
}

.captcha-input {
  flex: 1;
}

.captcha-image {
  width: 100px;
  height: 40px;
  background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 18px;
  color: #333;
  cursor: pointer;
  user-select: none;
  transition: all 0.3s;
}

.captcha-image:hover {
  background: linear-gradient(45deg, #e0e0e0, #d0d0d0);
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.login-button {
  margin-top: 10px;
}

.other-login {
  text-align: center;
  color: #666;
  font-size: 14px;
}

.other-login span {
  margin-right: 10px;
}

.register-link {
  text-align: center;
  color: #666;
  font-size: 14px;
}

:deep(.el-input__wrapper) {
  border-radius: 20px;
}

:deep(.el-button) {
  border-radius: 20px;
}

@media (max-width: 480px) {
  .login-box {
    padding: 30px 20px;
    margin: 10px;
  }
  
  .captcha-wrapper {
    flex-direction: column;
    align-items: stretch;
  }
  
  .captcha-image {
    width: 100%;
    margin-top: 10px;
  }
}
</style>