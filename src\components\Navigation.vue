<template>
  <div class="navigation">
    <el-menu
      :default-active="activeIndex"
      class="el-menu-demo"
      mode="horizontal"
      @select="handleSelect"
      background-color="#545c64"
      text-color="#fff"
      active-text-color="#ffd04b"
    >
      <el-menu-item index="/">
        <el-icon><House /></el-icon>
        <span>首页</span>
      </el-menu-item>
      
      <el-sub-menu index="management" v-if="isLoggedIn">
        <template #title>
          <el-icon><Setting /></el-icon>
          <span>管理中心</span>
        </template>
        <el-menu-item index="/users">
          <el-icon><User /></el-icon>
          <span>用户管理</span>
        </el-menu-item>
        <el-menu-item index="/categories">
          <el-icon><Menu /></el-icon>
          <span>类别管理</span>
        </el-menu-item>
      </el-sub-menu>

      <div class="flex-grow" />
      
      <el-menu-item index="login" v-if="!isLoggedIn">
        <el-icon><UserFilled /></el-icon>
        <span>登录</span>
      </el-menu-item>
      
      <el-sub-menu index="user" v-if="isLoggedIn">
        <template #title>
          <el-icon><UserFilled /></el-icon>
          <span>{{ username }}</span>
        </template>
        <el-menu-item index="profile">
          <el-icon><User /></el-icon>
          <span>个人资料</span>
        </el-menu-item>
        <el-menu-item index="logout">
          <el-icon><SwitchButton /></el-icon>
          <span>退出登录</span>
        </el-menu-item>
      </el-sub-menu>
    </el-menu>
  </div>
</template>

<script setup name="NavigationBar">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  House,
  Setting,
  User,
  Menu,
  UserFilled,
  SwitchButton
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()

// 响应式数据
const activeIndex = ref('/')
const username = ref('')

// 计算属性
const isLoggedIn = computed(() => {
  return localStorage.getItem('isLoggedIn') === 'true'
})

// 菜单选择处理
const handleSelect = async (key) => {
  if (key === 'login') {
    router.push('/login')
  } else if (key === 'logout') {
    await handleLogout()
  } else if (key === 'profile') {
    ElMessage.info('个人资料功能开发中...')
  } else if (key.startsWith('/')) {
    router.push(key)
  }
}

// 退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '确认退出',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 清除登录状态和token
    localStorage.removeItem('isLoggedIn')
    localStorage.removeItem('username')
    localStorage.removeItem('token')

    ElMessage.success('已退出登录')
    router.push('/')
  } catch (error) {
    // 用户取消退出
  }
}

// 更新当前激活的菜单项
const updateActiveIndex = () => {
  activeIndex.value = route.path
}

// 获取用户名
const getUserInfo = () => {
  if (isLoggedIn.value) {
    username.value = localStorage.getItem('username') || '用户'
  }
}

// 组件挂载时初始化
onMounted(() => {
  updateActiveIndex()
  getUserInfo()
  
  // 监听路由变化
  router.afterEach(() => {
    updateActiveIndex()
    getUserInfo()
  })
})
</script>

<style scoped>
.navigation {
  margin-bottom: 20px;
}

.el-menu-demo {
  border-bottom: solid 1px #e6e6e6;
}

.flex-grow {
  flex-grow: 1;
}

.el-menu--horizontal > .el-menu-item:last-child {
  float: right;
}
</style>
