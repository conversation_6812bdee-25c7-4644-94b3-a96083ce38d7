// utils/userApi.js
import request from './request.js'

// 用户管理相关API
export const userApi = {
  // 获取用户列表
  getUserList(params = {}) {
    return request.get('/user/list', params)
  },

  // 根据ID获取用户详情
  getUserById(id) {
    return request.get(`/user/${id}`)
  },

  // 添加用户
  addUser(userData) {
    return request.post('/user/add', userData)
  },

  // 更新用户信息
  updateUser(id, userData) {
    return request.put(`/user/update/${id}`, userData)
  },

  // 删除用户
  deleteUser(id) {
    return request.delete(`/user/delete/${id}`)
  },

  // 批量删除用户
  batchDeleteUsers(ids) {
    return request.post('/user/batchDelete', { ids })
  },

  // 搜索用户
  searchUsers(keyword, params = {}) {
    return request.get('/user/search', { keyword, ...params })
  },

  // 更新用户状态（启用/禁用）
  updateUserStatus(id, status) {
    return request.put(`/user/status/${id}`, { status })
  }
}

export default userApi
