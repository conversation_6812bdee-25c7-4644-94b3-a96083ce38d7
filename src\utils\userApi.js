// utils/userApi.js
import request from './request.js'

// 用户管理相关API
export const userApi = {
  // 获取用户列表
  getUserList() {
    return request.get('/userAPI/findAll')
  },

  // 根据ID获取用户详情
  getUserById(id) {
    return request.get(`/userAPI/${id}`)
  },

  // 添加用户 - 使用统一的保存或更新接口
  addUser(userData) {
    // 新增时不传id
    // eslint-disable-next-line no-unused-vars
    const { id, ...userDataWithoutId } = userData
    return request.post('/userAPI/saveOrUpdate', userDataWithoutId)
  },

  // 更新用户信息 - 使用统一的保存或更新接口
  updateUser(id, userData) {
    // 更新时必须包含ID
    return request.post('/userAPI/saveOrUpdate', { ...userData, id })
  },

  // 删除用户
  deleteUser(id) {
    return request.delete(`/userAPI/delete/${id}`)
  },

  // 批量删除用户
  batchDeleteUsers(ids) {
    return request.post('/userAPI/batchDelete', { ids })
  },

  // 搜索用户（如果后端支持）
  searchUsers(keyword, params = {}) {
    return request.get('/userAPI/search', { keyword, ...params })
  },

  // 更新用户状态（如果后端支持）
  updateUserStatus(id, status) {
    return request.put(`/userAPI/status/${id}`, { status })
  }
}

export default userApi
