// utils/userApi.js
import request from './request.js'

// 用户管理相关API
export const userApi = {
  // 获取用户列表
  getUserList(params = {}) {
    return request.get('/userAPI/list', params)
  },

  // 根据ID获取用户详情
  getUserById(id) {
    return request.get(`/userAPI/${id}`)
  },

  // 添加用户 - 使用统一的保存或更新接口
  addUser(userData) {
    return request.post('/userAPI/saveOrUpdate', userData)
  },

  // 更新用户信息 - 使用统一的保存或更新接口
  updateUser(id, userData) {
    // 确保包含ID以便后端识别为更新操作
    return request.post('/userAPI/saveOrUpdate', { ...userData, id })
  },

  // 删除用户
  deleteUser(id) {
    return request.delete(`/userAPI/delete/${id}`)
  },

  // 批量删除用户
  batchDeleteUsers(ids) {
    return request.post('/userAPI/batchDelete', { ids })
  },

  // 搜索用户
  searchUsers(keyword, params = {}) {
    return request.get('/userAPI/search', { keyword, ...params })
  },

  // 更新用户状态（启用/禁用）
  updateUserStatus(id, status) {
    return request.put(`/userAPI/status/${id}`, { status })
  }
}

export default userApi
